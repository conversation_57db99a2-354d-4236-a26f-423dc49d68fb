from enum import IntEnum, Enum


# 都是收盘强制平仓，实际上涨触发和下跌触发同理
class TradeMode(IntEnum):
    # 上涨触发看多开仓，如果盘中涨到目标价格就平仓（止盈），或者下跌触发看空开仓，如果盘中跌到目标价格就平仓（止盈）
    SAME_TREND_STOP_PROFIT = 1
    # 上涨触发看多开仓，如果盘中跌到目标价格就平仓（止损），或者下跌触发看空开仓，如果盘中涨到目标价格就平仓（止损）
    SAME_TREND_STOP_LOSS = -2
    # 上涨触发看空开仓，如果盘中跌到目标价格就平仓（止盈），或者下跌触发看多开仓，如果盘中涨到目标价格就平仓（止盈）
    REVERSE_TREND_STOP_PROFIT = 2
    # 上涨触发看空开仓，如果盘中涨到目标价格就平仓（止损），或者下跌触发看多开仓，如果盘中跌到目标价格就平仓（止损）
    REVERSE_TREND_STOP_LOSS = -1


class Direction(str, Enum):
    # 看涨
    UP = "BUY"
    # 看跌
    DOWN = "SELL"


class Status(str, Enum):
    # 满足开仓的条件（待开仓）
    OPEN_ATTEMPT = "预开仓"
    # 实际开仓成功
    OPEN_SUCCESS = "开仓成功"
    OPEN_FAIL = "开仓失败"

    CLOSE_ATTEMPT = "预平仓"
    CLOSE_SUCCESS = "平仓成功"
    CLOSE_FAIL = "平仓失败"
