from tqsdk import TqApi

from entity.trade_base import Direction, Status
from entity.param_instance import ParamInstance
from new_trade.base_trade_executor import BaseTradeExecutor


class TqTradeExecutor(BaseTradeExecutor):
    def __init__(self, data_dir, api: TqApi, position=None, max_volume_per_trade: int = None):
        """
        Args:
            data_dir: 数据目录
            api: 天勤API实例
            position: 持仓信息
            max_volume_per_trade: 单次开仓最大手数，如果为None则使用默认值
        """
        super().__init__(data_dir, max_volume_per_trade)
        self.api = api
        self.position = position
        self.target_pos_tasks = {}

    def open(self, open_attempt_instance: ParamInstance):
        # ToDo(hm): from here
        order = self.api.insert_order(open_attempt_instance.contract_code,
                                      direction=open_attempt_instance.direction.value, offset="OPEN",
                                      volume=open_attempt_instance.open_volume)

    def open_deprecated(self, open_attempt_instance: ParamInstance):
        try:
            # 实际执行开仓
            # 这里可以根据实际需求实现具体的开仓逻辑
            # 例如使用TargetPosTask设置目标持仓量
            # ToDo(hm): 这是 ai 生成的方案，改成之前 qiu 实现的
            contract_code = open_attempt_instance.contract_code
            if contract_code not in self.target_pos_tasks:
                try:
                    from tqsdk import TargetPosTask
                    print(f"Creating TargetPosTask for contract: {contract_code}")
                    self.target_pos_tasks[contract_code] = TargetPosTask(self.api, contract_code)
                except ImportError:
                    from tqsdk2 import TargetPosTask
                    print(f"Creating TargetPosTask for contract: {contract_code}")
                    self.target_pos_tasks[contract_code] = TargetPosTask(self.api, contract_code)

            # 设置目标持仓量，这里简单设置为1手
            target_pos = 1 if open_attempt_instance.direction == Direction.UP else -1
            print(f"Setting target position for {contract_code} to {target_pos}")
            self.target_pos_tasks[contract_code].set_target_volume(target_pos)
            # 更新状态
            open_attempt_instance.status = Status.OPEN_SUCCESS
            print(f"Opening position: {open_attempt_instance.code} at price {open_attempt_instance.open_price}")
            # 保存实例数据
            instance_path = self.upsert_instance(open_attempt_instance)
            print(f"Instance data saved to: {instance_path}")

        except Exception as e:
            print(f"Error in TQ open: {e}")
            import traceback
            traceback.print_exc()
            open_attempt_instance.status = Status.OPEN_FAIL

    def close(self, close_attempt_instance: ParamInstance):
        pass

    def close_deprecated(self, close_attempt_instance: ParamInstance):
        try:
            # 实际执行平仓
            # 这里可以根据实际需求实现具体的平仓逻辑
            # 使用cat作为实际的合约代码，而不是param_code
            contract_code = close_attempt_instance.cat
            if contract_code in self.target_pos_tasks:
                # 平仓，设置目标持仓量为0
                print(f"Closing position for {contract_code}")
                self.target_pos_tasks[contract_code].set_target_volume(0)

            # 更新状态
            close_attempt_instance.status = Status.CLOSE_SUCCESS
            print(
                f"Closing position: {close_attempt_instance.code} at price {close_attempt_instance.actual_close_price}")
            print(f"Profit: {close_attempt_instance.profit}")

            # 保存实例数据
            instance_path = self.upsert_instance(close_attempt_instance)
            print(f"Instance data saved to: {instance_path}")

        except Exception as e:
            print(f"Error in TQ close: {e}")
            import traceback
            traceback.print_exc()
            close_attempt_instance.status = Status.CLOSE_FAIL
