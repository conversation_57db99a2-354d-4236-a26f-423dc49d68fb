import os
import time
import sys

import pandas as pd

from constant.constant import TQ_TIME_OUT, CONFIG_FILE_NAME, RUN_NUM, STATUS, GOING_ON, PID, PARAM_DF_NAME, CAT, \
    ACCOUNT, CODE, USE_API_CODE, DRY_RUN, PARAM_VERSION, BACKTEST_START_TIME, BACKTEST_END_TIME
from entity.trade import TradeResult, TradeInput
from entity.trade_base import TradeMode
from entity.param import Param
from new_trade.base_trade_executor import BaseTradeExecutor
from new_trade.tq_trade_executor import TqTradeExecutor
from new_trade.trade_strategy import TradeStrategy
from util.common_util import get_code_version, is_empty
from util.email_util import send_warn_email
from util.file_util import join_path, load_json, update_json_file
from util.tq_util import get_code_from_tq
from util.trade_util import get_time_price_list

try:
    from tqsdk2 import Tq<PERSON><PERSON>, Tq<PERSON><PERSON>, TqSim, TqBacktest, TargetPosTask, BacktestFinished, TqAccount
except ImportError:
    try:
        from tqsdk import Tq<PERSON><PERSON>, TqAuth, TqSim, TqBacktest, TargetPosTask, BacktestFinished, TqAccount
    except ImportError:
        print("Error: Neither tqsdk2 nor tqsdk could be imported. Please install one of them.")
        sys.exit(1)


def adapt_to_run_risk(checkpoint_path, data_dir, user_name, password, project_dir=None, send_email=True):
    config_path = join_path(checkpoint_path, CONFIG_FILE_NAME)
    run_config_dict = load_json(config_path)
    run_num = run_config_dict[RUN_NUM] + 1 if RUN_NUM in run_config_dict else 1
    # ToDo(hm): not PENDING, should add more status
    update_json_file(config_path, {STATUS: GOING_ON, PID: os.getpid(), RUN_NUM: run_num})
    account_name = run_config_dict[ACCOUNT]
    cat = run_config_dict[CAT]
    code = run_config_dict[CODE]
    use_api_code = USE_API_CODE in run_config_dict and run_config_dict[USE_API_CODE]
    param_df_path = join_path(checkpoint_path, PARAM_DF_NAME)
    param_df = pd.read_csv(param_df_path)
    param_list = list()
    param_version = run_config_dict[PARAM_VERSION]
    for row_idx, row in param_df.iterrows():
        param_code = f"{param_version}_{row_idx}"
        param = Param(
            code=param_code,
            cat=cat,
            account_name=account_name,
            contract_code=code,
            delta_time=row["tr"],
            # delta_percent 实际上要 /100
            delta_percent=row["dp"] / 100,
            alpha=row["al"],
            trade_mode=TradeMode(row["tm"])
        )
        param_list.append(param)
    if run_config_dict[DRY_RUN]:
        if use_api_code:
            try:
                code = get_code_from_tq(data_dir, account_name, cat)
            except RuntimeError as e:
                err_msg = f"账号{account_name}的{cat}{code}风控告警: API 获取合约代码失败，将会使用默认合约{code}"
                if send_email:
                    send_warn_email(data_dir, err_msg, content=f"代码版本: {get_code_version(project_dir)}")

        backtest_start_time = run_config_dict[BACKTEST_START_TIME]
        backtest_end_time = run_config_dict[BACKTEST_END_TIME]
        trade_input = TradeInput(param_list, code, user_name=user_name, password=password,
                                 start_time=backtest_start_time, end_time=backtest_end_time)
        start_time = pd.to_datetime(trade_input.start_time)
        end_time = pd.to_datetime(trade_input.end_time)
        tqacc = TqSim(trade_input.init_fund)
        api = TqApi(tqacc,
                    backtest=TqBacktest(start_dt=start_time, end_dt=end_time),
                    auth=TqAuth(trade_input.user_name, trade_input.password), web_gui=False)
        position = api.get_position(trade_input.code)
        trade_executor = TqTradeExecutor(data_dir, api, position)
        trade_result = run_risk(trade_input, api, trade_executor)
        return trade_result
    return None


# ToDo(hm): 实现重启任务，重新加载 instance
# ToDo(hm): 如果线上用这个该怎么修改呢？
# ToDo(hm): rename this
def run_risk(trade_input: TradeInput, api, trade_executor: BaseTradeExecutor) -> TradeResult:
    update_last_time = pd.to_datetime(trade_input.update_start_time)
    data_length = max([param.delta_time for param in trade_input.param_list]) + 1
    # 返回的K线序列数据是从当前最新一根K线开始往回取data_length根
    klines = api.get_kline_serial(trade_input.code, 1, data_length=data_length)
    trade_strategy = TradeStrategy(trade_executor, cd_ratio=trade_input.cd_ratio)
    # 记录所有开仓和平仓实例
    all_open_instances = list()
    all_close_instances = list()
    total_time_list = list()
    total_price_list = list()
    try:
        while True:
            # 等待行情更新
            is_updated = api.wait_update(deadline=time.time() + TQ_TIME_OUT)
            if not is_updated:
                print("No update received within timeout period")
                continue

            if not api.is_changing(klines):
                continue

            # 获取当前K线时间
            kline_time = pd.to_datetime(klines.iloc[-1]['datetime'] + 28800 * 1e9, unit='ns')
            time_delta = kline_time - update_last_time

            # 如果时间变化太小，跳过
            if time_delta.seconds < 1:
                continue

            # 更新上次处理时间
            update_last_time = kline_time
            if kline_time.minute % 5 == 0 and kline_time.second == 0:
                print(f"kline_time={kline_time}")

            # ToDo(hm): 区分线上和测试
            # if kline_time.second % 5 == 0:
            #     print(f"kline_time={kline_time}")

            # 获取时间和价格列表
            time_list, price_list = get_time_price_list(klines)

            if len(time_list) == 0 or len(price_list) == 0:
                print("Warning: Empty time or price list")
                continue

            # 存储所有时间价格数据
            if is_empty(total_time_list):
                total_time_list += time_list
                total_price_list += price_list
            else:
                for idx in range(len(time_list)):
                    # 找到第一个比 total_time_list[-1] 大的时间，并追加
                    if time_list[idx] > total_time_list[-1]:
                        total_time_list += time_list[idx:]
                        total_price_list += price_list[idx:]
                        break

            # 尝试开仓
            open_attempt_instance_list = trade_strategy.attempt_open_instance(trade_input.param_list, price_list,
                                                                              time_list)
            if open_attempt_instance_list:
                print(f"Opened {len(open_attempt_instance_list)} positions")
                for instance in open_attempt_instance_list:
                    print(
                        f"  - {instance.contract_code}: price={instance.open_price}, target={instance.expected_close_price}")
                all_open_instances.extend(open_attempt_instance_list)

            # 尝试平仓
            close_attempt_instance_list = trade_strategy.attempt_close_instance(trade_input.param_list, price_list,
                                                                                time_list)
            if close_attempt_instance_list:
                print(f"Closed {len(close_attempt_instance_list)} positions")
                for instance in close_attempt_instance_list:
                    print(
                        f"  - {instance.contract_code}: price={instance.actual_close_price}, profit={instance.profit}")
                all_close_instances.extend(close_attempt_instance_list)
        return TradeResult(trade_input.param_list, total_time_list, total_price_list)
    except KeyboardInterrupt:
        print("Trading interrupted by user")
    except BacktestFinished:
        print("Backtest finished")
        return TradeResult(trade_input.param_list, total_time_list, total_price_list)

    finally:
        # 关闭API连接
        api.close()


if __name__ == '__main__':
    # # 获取所有交易实例
    # all_instances = TradeStrategy.get_instance_list(param_list)
    #
    # # 计算总利润
    # total_profit = sum(instance.profit for instance in all_instances if instance.status == 'CLOSE_SUCCESS')
    # print(f"Total profit: {total_profit}")
    #
    # # 显示所有完成的交易
    # closed_instances = [instance for instance in all_instances if instance.status == 'CLOSE_SUCCESS']
    # if closed_instances:
    #     print("\nCompleted trades:")
    #     for idx, instance in enumerate(closed_instances, 1):
    #         print(
    #             f"  {idx}. {instance.code}: open={instance.open_price}, close={instance.actual_close_price}, profit={instance.profit}")
    #
    # draw_line(total_price_list, instances=all_instances, time_list=total_time_list)
    pass

    # 可以在这里自定义参数，或者使用默认参数

    # 示例1：使用默认参数运行
    # main()

    # 示例2：自定义参数运行
    # params = get_default_params()
    # main(param_list=params,
    #      start_time="2025-04-25 09:00:00",
    #      end_time="2025-04-25 15:00:00",
    #      cd_ratio=2.0,  # 减少冷却时间
    #      data_length=2000)  # 增加数据长度

    # 示例3
    # t_18-p_0.2-a_0.9-g_1s-m_1@1-v_6-op_5999-tp_6009.8-pp_5987-dp_12-dp%_0.20
