import unittest

import pandas as pd
from tqsdk import TqSim, TqApi, TqBacktest, TqAuth

from TqMockApi import TqMockA<PERSON>
from constant.constant import CZCE_SR, TIME_COL, PRICE_COL, CHECKPOINT_DIR
from entity.trade import TradeInput
from entity.trade_base import TradeMode
from entity.param_instance import ParamInstance
from entity.param import Param
from new_trade.mock_trade_executor import MockTradeExecutor
from new_trade.tq_trade_executor import TqTradeExecutor
from new_trade.visualize import draw_line
from risk_adaptor import run_risk, adapt_to_run_risk
from unit_test.base import TEST_DATA_ROOT_DIR_IGNORED, TEST_DATA_ROOT_DIR, EXPECTED_DIR
from util.common_util import assert_eq
from util.file_util import rmdir_if_exists, mkdir_if_not_exists, join_path, load_json
from util.trade_util import get_instance_list, merge_to_instance_df


class TestTradeTq(unittest.TestCase):
    def setUp(self):
        """在每个测试方法执行前清空测试数据目录"""
        # 清空测试数据目录
        rmdir_if_exists(TEST_DATA_ROOT_DIR_IGNORED)
        # 重新创建测试数据目录
        mkdir_if_not_exists(TEST_DATA_ROOT_DIR_IGNORED)

    def tearDown(self):
        """在每个测试方法执行后清空测试数据目录"""
        # 清空测试数据目录
        # rmdir_if_exists(TEST_DATA_ROOT_DIR_IGNORED)
        pass

    def test_sr505_0310_by_mock_api(self):
        contract_code = "CZCE.SR505"
        custom_param = Param(
            code="param_1",
            account_name="fx",
            cat=CZCE_SR,
            contract_code=contract_code,
            delta_time=116,
            delta_percent=0.004,
            alpha=2.0,
            trade_mode=TradeMode.SAME_TREND_STOP_LOSS,
        )
        trade_input = TradeInput(param_list=[custom_param],
                                 code=contract_code,
                                 init_fund=********,
                                 start_time="2025-03-10 09:03:00",
                                 end_time="2025-03-10 09:10:00",
                                 update_start_time="2025-03-10 09:03:00",
                                 cd_ratio=1.0)
        start_time = pd.to_datetime(trade_input.start_time)
        end_time = pd.to_datetime(trade_input.end_time)
        data_path = join_path(TEST_DATA_ROOT_DIR, "test_sr505_0310_price.csv")
        api = TqMockApi(data_path=data_path, start_dt=start_time, end_dt=end_time)
        trade_executor = MockTradeExecutor(TEST_DATA_ROOT_DIR_IGNORED, 100)
        trade_result = run_risk(trade_input, api, trade_executor)
        all_instances = get_instance_list(trade_result.param_list)
        save_path = join_path(TEST_DATA_ROOT_DIR_IGNORED, f"test_sr505_0310_by_mock_api.svg")
        draw_line(trade_result.price_list, instances=all_instances, time_list=trade_result.time_list,
                  save_path=save_path)
        assert_eq(1, len(all_instances))
        expected_path = join_path(TEST_DATA_ROOT_DIR, EXPECTED_DIR, "test_sr505_0310_1.json")
        expected_instance_dict = load_json(expected_path)
        expected_instance = ParamInstance.from_dict(expected_instance_dict)
        actual_instance = all_instances[0]
        assert_eq(expected_instance, actual_instance)

    # @unittest.skip("用实际的 tqApi")
    def test_sr505_0310(self):
        contract_code = "CZCE.SR505"
        custom_param = Param(
            code="param_1",
            account_name="fx",
            cat=CZCE_SR,
            contract_code=contract_code,
            delta_time=116,
            delta_percent=0.004,
            alpha=2.0,
            trade_mode=TradeMode.SAME_TREND_STOP_LOSS,
        )
        # Load account info
        account_info_path = join_path(TEST_DATA_ROOT_DIR, "account_info.json")
        account_info = load_json(account_info_path)
        trade_input = TradeInput(param_list=[custom_param],
                                 code=contract_code,
                                 user_name=account_info["TQ_USER_NAME"],
                                 password=account_info["TQ_PASSWORD"],
                                 init_fund=********,
                                 start_time="2025-03-10 09:03:00",
                                 end_time="2025-03-10 09:10:00",
                                 update_start_time="2025-03-10 09:03:00",
                                 cd_ratio=1.0)
        start_time = pd.to_datetime(trade_input.start_time)
        end_time = pd.to_datetime(trade_input.end_time)
        tqacc = TqSim(trade_input.init_fund)
        api = TqApi(tqacc,
                    backtest=TqBacktest(start_dt=start_time, end_dt=end_time),
                    auth=TqAuth(trade_input.user_name, trade_input.password), web_gui=False)
        position = api.get_position(trade_input.code)
        trade_executor = TqTradeExecutor(TEST_DATA_ROOT_DIR_IGNORED, api, position)
        trade_result = run_risk(trade_input, api, trade_executor)
        all_instances = get_instance_list(trade_result.param_list)
        save_path = join_path(TEST_DATA_ROOT_DIR_IGNORED, f"test_sr505_0310.svg")
        draw_line(trade_result.price_list, instances=all_instances, time_list=trade_result.time_list,
                  save_path=save_path)
        # save data for mock
        # price_df = pd.DataFrame({TIME_COL: trade_result.time_list, PRICE_COL: trade_result.price_list})
        # save_path = join_path(TEST_DATA_ROOT_DIR, f"test_sr505_0310_price.csv")
        # price_df.to_csv(save_path, index=False)
        instance_df = merge_to_instance_df(all_instances)
        save_path = join_path(TEST_DATA_ROOT_DIR_IGNORED, f"test_sr505_0310_instance.csv")
        instance_df.to_csv(save_path, index=False)
        assert_eq(1, len(all_instances))
        expected_path = join_path(TEST_DATA_ROOT_DIR, EXPECTED_DIR, f"test_sr505_0310_1.json")
        expected_instance_dict = load_json(expected_path)
        expected_instance = ParamInstance.from_dict(expected_instance_dict)
        actual_instance = all_instances[0]
        assert_eq(expected_instance, actual_instance)

    # @unittest.skip("用实际的 tqApi")
    def test_sr505_0225(self):
        test_name = "test_sr505_0225"
        # ToDo(hm): 把这个也变成 TqMockApi 的
        ckp_path = join_path(TEST_DATA_ROOT_DIR, CHECKPOINT_DIR, "2025-02-25_17-00-10")
        data_dir = TEST_DATA_ROOT_DIR_IGNORED
        account_info_path = join_path(TEST_DATA_ROOT_DIR, "account_info.json")
        account_info = load_json(account_info_path)
        trade_result = adapt_to_run_risk(ckp_path, data_dir, user_name=account_info["TQ_USER_NAME"],
                                         password=account_info["TQ_PASSWORD"], )
        all_instances = get_instance_list(trade_result.param_list)
        save_path = join_path(TEST_DATA_ROOT_DIR_IGNORED, f"{test_name}.svg")
        draw_line(trade_result.price_list, instances=all_instances, time_list=trade_result.time_list,
                  save_path=save_path)
        instance_df = merge_to_instance_df(instance_list=all_instances)
        save_path = join_path(TEST_DATA_ROOT_DIR_IGNORED, f"{test_name}_instance.csv")
        instance_df.to_csv(save_path, index=False)
        # save data for mock
        # price_df = pd.DataFrame({TIME_COL: trade_result.time_list, PRICE_COL: trade_result.price_list})
        # save_path = join_path(TEST_DATA_ROOT_DIR, f"{test_name}_price.csv")
        # price_df.to_csv(save_path, index=False)


if __name__ == "__main__":
    unittest.main()
