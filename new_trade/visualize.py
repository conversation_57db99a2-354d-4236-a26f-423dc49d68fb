import os
from typing import List, Dict, Optional
import numpy as np

from matplotlib import pyplot as plt

from constant.constant import TIME_COL, PRICE_COL
from entity.param_instance import ParamInstance
from entity.trade_base import Direction, Status, TradeMode
from util.common_util import not_empty


def draw_line(price_list, instances: Optional[List[ParamInstance]] = None, time_list=None, save_path=None):
    plt.figure(figsize=(12, 6))

    # 绘制价格曲线
    if time_list is not None:
        # 创建连续的索引作为 x 轴
        indices = np.arange(len(time_list))

        # 创建时间到索引的映射，用于标注交易信号
        time_to_index: Dict[str, int] = {}
        for i, t in enumerate(time_list):
            time_to_index[t.strftime("%Y-%m-%d %H:%M:%S")] = i

        # 使用连续索引绘制价格曲线
        plt.plot(indices, price_list, label="Price", alpha=0.7)

        # 设置 x 轴刻度和标签
        # 选择合适的刻度位置（根据数据量调整）
        tick_step = max(1, len(indices) // 10)  # 最多显示10个刻度
        tick_positions = indices[::tick_step]
        tick_labels = [time_list[i].strftime("%m-%d %H:%M") for i in tick_positions]

        plt.xticks(tick_positions, tick_labels, rotation=45)
    else:
        # 使用索引作为 x 轴
        plt.plot(price_list, label="Price", alpha=0.7)

    if not_empty(instances):
        # 创建字典来跟踪每个位置的实例数量
        open_position_counts = {}
        open_position_instances = {}
        close_position_counts = {}
        close_position_instances = {}

        # 第一次遍历：计算每个位置的实例数量
        for inst in instances:
            # 如果使用时间序列，需要将时间转换为对应的索引
            if time_list is not None:
                open_time_str = inst.open_time.strftime("%Y-%m-%d %H:%M:%S")
                # 找到最接近的时间点
                if open_time_str in time_to_index:
                    open_idx = time_to_index[open_time_str]
                else:
                    # 找到最接近的时间点
                    closest_time = min(time_list, key=lambda t: abs(t - inst.open_time))
                    open_idx = time_list.index(closest_time)

                # 创建开仓位置键（时间索引和价格）
                open_position_key = (open_idx, inst.open_price)

                # 增加该开仓位置的计数
                if open_position_key in open_position_counts:
                    open_position_counts[open_position_key] += 1
                    open_position_instances[open_position_key].append(inst)
                else:
                    open_position_counts[open_position_key] = 1
                    open_position_instances[open_position_key] = [inst]
                
                # 如果已平仓，记录平仓位置
                if inst.status == Status.CLOSE_SUCCESS:
                    close_time_str = inst.close_time.strftime("%Y-%m-%d %H:%M:%S")
                    # 找到最接近的时间点
                    if close_time_str in time_to_index:
                        close_idx = time_to_index[close_time_str]
                    else:
                        # 找到最接近的时间点
                        closest_time = min(time_list, key=lambda t: abs(t - inst.close_time))
                        close_idx = time_list.index(closest_time)
                    
                    # 创建平仓位置键（时间索引和价格）
                    close_position_key = (close_idx, inst.actual_close_price)
                    
                    # 增加该平仓位置的计数
                    if close_position_key in close_position_counts:
                        close_position_counts[close_position_key] += 1
                        close_position_instances[close_position_key].append(inst)
                    else:
                        close_position_counts[close_position_key] = 1
                        close_position_instances[close_position_key] = [inst]

        # 标注交易信号
        for inst in instances:
            # 如果使用时间序列，需要将时间转换为对应的索引
            if time_list is not None:
                open_time_str = inst.open_time.strftime("%Y-%m-%d %H:%M:%S")
                # 找到最接近的时间点
                if open_time_str in time_to_index:
                    open_idx = time_to_index[open_time_str]
                else:
                    # 找到最接近的时间点
                    closest_time = min(time_list, key=lambda t: abs(t - inst.open_time))
                    open_idx = time_list.index(closest_time)

                # 开仓标注
                plt.scatter(
                    open_idx,
                    inst.open_price,
                    color="green" if inst.direction == Direction.UP else "blue",
                    marker="^",
                    s=100,
                    label="Open (Long)" if inst.direction == Direction.UP else "Open (Short)",
                )

                # 获取该开仓位置的实例数量
                open_position_key = (open_idx, inst.open_price)
                count = open_position_counts[open_position_key]
                instances_at_position = open_position_instances[open_position_key]

                # 只有当实例是该位置的最后一个时才标注信息
                if instances_at_position and inst == instances_at_position[-1]:
                    # 在开仓位置标注实例信息
                    description = f"{inst.code}\n{inst.open_time}\n{inst.open_price}"

                    # 如果有多个实例，添加计数
                    if count > 1:
                        description += f"\nnum:{count}"

                    plt.annotate(
                        description,
                        (open_idx, inst.open_price),
                        xytext=(5, 10),  # 文本偏移量
                        textcoords="offset points",
                        fontsize=8,
                        bbox=dict(boxstyle="round,pad=0.3", fc="yellow", alpha=0.7),
                    )
            else:
                # 使用原始时间
                plt.scatter(
                    inst.open_time,
                    inst.open_price,
                    color="green" if inst.direction == Direction.UP else "blue",
                    marker="^",
                    s=100,
                    label="Open (Long)" if inst.direction == Direction.UP else "Open (Short)",
                )

                # 创建开仓位置键（时间和价格）
                open_position_key = (inst.open_time, inst.open_price)
                
                # 获取该开仓位置的实例数量
                count = open_position_counts.get(open_position_key, 0)
                instances_at_position = open_position_instances.get(open_position_key, [])
                
                # 只有当实例是该位置的最后一个时才标注信息
                if instances_at_position and inst == instances_at_position[-1]:
                    # 在开仓位置标注实例代码
                    code_text = inst.code

                    # 如果有多个实例，添加计数
                    if count > 1:
                        code_text += f" num:{count}"

                    plt.annotate(
                        code_text,
                        (inst.open_time, inst.open_price),
                        xytext=(5, 10),  # 文本偏移量
                        textcoords="offset points",
                        fontsize=8,
                        bbox=dict(boxstyle="round,pad=0.3", fc="yellow", alpha=0.7),
                    )

            # 平仓标注
            if inst.status == Status.CLOSE_SUCCESS:
                if time_list is not None:
                    close_time_str = inst.close_time.strftime("%Y-%m-%d %H:%M:%S")
                    # 找到最接近的时间点
                    if close_time_str in time_to_index:
                        close_idx = time_to_index[close_time_str]
                    else:
                        # 找到最接近的时间点
                        closest_time = min(time_list, key=lambda t: abs(t - inst.close_time))
                        close_idx = time_list.index(closest_time)

                    plt.scatter(
                        close_idx,
                        inst.actual_close_price,
                        color="red",
                        marker="v",
                        s=100,
                        label="Close",
                    )
                    
                    # 获取该平仓位置的实例数量
                    close_position_key = (close_idx, inst.actual_close_price)
                    close_count = close_position_counts.get(close_position_key, 0)
                    close_instances_at_position = close_position_instances.get(close_position_key, [])
                    
                    # 只有当实例是该平仓位置的最后一个时才标注信息
                    if close_instances_at_position and inst == close_instances_at_position[-1]:
                        # 在平仓位置标注实例信息
                        description = f"{inst.code}\n{inst.close_time}\n{inst.actual_close_price}"
                        
                        # 如果有多个实例，添加计数
                        if close_count > 1:
                            description += f"\nnum:{close_count}"
                        
                        plt.annotate(
                            description,
                            (close_idx, inst.actual_close_price),
                            xytext=(5, 10),  # 文本偏移量
                            textcoords="offset points",
                            fontsize=8,
                            bbox=dict(boxstyle="round,pad=0.3", fc="lightblue", alpha=0.7),
                        )

                    # 绘制预期线段
                    plt.plot(
                        [open_idx, close_idx],
                        [inst.open_price, inst.actual_close_price],
                        linestyle="--",
                        alpha=0.3,
                        color="gray",
                    )
                else:
                    plt.scatter(
                        inst.close_time,
                        inst.actual_close_price,
                        color="red",
                        marker="v",
                        s=100,
                        label="Close",
                    )
                    
                    # 创建平仓位置键（时间和价格）
                    close_position_key = (inst.close_time, inst.actual_close_price)
                    
                    # 获取该平仓位置的实例数量
                    close_count = close_position_counts.get(close_position_key, 0)
                    close_instances_at_position = close_position_instances.get(close_position_key, [])
                    
                    # 只有当实例是该平仓位置的最后一个时才标注信息
                    if close_instances_at_position and inst == close_instances_at_position[-1]:
                        # 在平仓位置标注实例代码
                        code_text = f"{inst.code}"
                        
                        # 如果有多个实例，添加计数
                        if close_count > 1:
                            code_text += f" num:{close_count}"
                        
                        plt.annotate(
                            code_text,
                            (inst.close_time, inst.actual_close_price),
                            xytext=(5, 10),  # 文本偏移量
                            textcoords="offset points",
                            fontsize=8,
                            bbox=dict(boxstyle="round,pad=0.3", fc="lightblue", alpha=0.7),
                        )

                    # 绘制预期线段
                    plt.plot(
                        [inst.open_time, inst.close_time],
                        [inst.open_price, inst.actual_close_price],
                        linestyle="--",
                        alpha=0.3,
                        color="gray",
                    )

    # 处理图例去重
    handles, labels = plt.gca().get_legend_handles_labels()
    by_label = dict(zip(labels, handles))
    plt.legend(by_label.values(), by_label.keys())

    plt.xlabel(TIME_COL)
    plt.ylabel(PRICE_COL)
    plt.grid(True)

    # 调整 x 轴格式
    if time_list is not None:
        plt.tight_layout()  # 自动调整布局，避免标签重叠

    if save_path is None:
        plt.show()
        return

    format = os.path.basename(save_path).split('.')[1]
    plt.savefig(save_path, format=format, bbox_inches='tight')


if __name__ == '__main__':
    # 基本示例：使用索引作为 x 轴
    # save_path = "./tmp.svg"
    # draw_line([1000, 1010, 1020, 1030, 1020, 1010], save_path=save_path)
    # exit()

    # 使用时间序列作为 x 轴的示例
    import datetime

    # 创建带有时间间隔的时间序列（模拟夜盘和日盘）
    night_session = [
        datetime.datetime(2023, 1, 1, 21, 0, 0) + datetime.timedelta(minutes=i)
        for i in range(120)  # 21:00-23:00
    ]

    day_session = [
        datetime.datetime(2023, 1, 2, 9, 0, 0) + datetime.timedelta(minutes=i)
        for i in range(360)  # 09:00-15:00
    ]

    # 合并时间序列
    times = night_session + day_session

    # 创建对应的价格序列
    prices = [1000 + i % 50 for i in range(len(times))]

    instance = ParamInstance(
        cat="example",
        account_name="example_account",
        code="example_instance",
        contract_code="example",  # 合约代码与品种一致
        param_code="example_param",
        direction=Direction.UP,
        status=Status.CLOSE_SUCCESS,
        open_price=1010,
        open_time=times[50],  # 夜盘的一个时间点
        expected_close_price=1030,
        actual_close_price=1020,
        close_time=times[200],  # 日盘的一个时间点
        profit=10,
        pre_price=1000,  # 设置开仓前的价格
        trade_mode=TradeMode.SAME_TREND_STOP_PROFIT  # 设置交易模式
    )

    # 显示带有时间序列的图表
    draw_line(prices, [instance], times)
